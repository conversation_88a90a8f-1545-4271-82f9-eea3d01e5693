# Stagewise工具栏完整功能清单

基于对stagewise源代码的深度分析，以下是完整的功能规范和实现要求。

## 1. 工具栏按钮配置

### 主工具栏（展开状态）- 3个核心按钮

#### 1.1 设置按钮 (Setting<PERSON> Button)
- **图标**: ⚙️ 齿轮图标
- **位置**: 第一个按钮（左侧）
- **功能**: 打开/关闭设置面板
- **状态指示**: 
  - 激活时: 蓝色背景 `bg-blue-500/30`
  - 默认时: 半透明背景 `bg-black/10`
- **点击行为**: 切换设置面板开启/关闭状态

#### 1.2 聊天按钮 (Chat Button) - 核心功能按钮
- **图标**: 💬 消息圆圈图标
- **位置**: 第二个按钮（中间）
- **功能**: AI交互的主入口
- **双重功能**:
  1. 打开聊天面板（包含文本输入框）
  2. 自动激活DOM元素选择模式
- **状态指示**:
  - 聊天面板打开时: 绿色背景 `bg-green-500/30`
  - 选择模式激活时: 脉冲动画效果
- **点击行为**: 
  - 第一次点击: 打开聊天面板 + 进入选择模式
  - 第二次点击: 退出选择模式，保持聊天面板打开

#### 1.3 最小化按钮 (Minimize Button)
- **图标**: ⬜ 收缩图标
- **位置**: 第三个按钮（右侧）
- **功能**: 最小化工具栏
- **点击行为**: 收缩工具栏为小圆点

### 最小化状态
- **显示**: 小圆形按钮（直径48px）
- **图标**: ⬆️ 展开图标或Logo
- **点击行为**: 恢复展开工具栏
- **样式**: 玻璃态效果，半透明背景

## 2. DOM元素选择功能

### 2.1 激活条件
- 点击聊天按钮时自动激活
- 聊天面板打开且处于prompt创建模式

### 2.2 选择流程
```
用户点击聊天按钮
    ↓
打开聊天面板 + 激活选择模式
    ↓
鼠标悬停元素 → 显示蓝色高亮框
    ↓
点击元素 → 添加到选中列表
    ↓
元素显示绿色边框 + 在聊天面板显示为chip
    ↓
用户输入文本描述
    ↓
发送消息（包含文本 + 选中元素上下文）
```

### 2.3 视觉反馈
- **悬停状态**: 
  - 蓝色边框 `border: 2px solid #3b82f6`
  - 半透明蓝色背景 `background: rgba(59, 130, 246, 0.1)`
  - 圆角 `border-radius: 4px`
  - 阴影效果 `box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3)`

- **选中状态**:
  - 绿色边框 `border: 2px solid #10b981`
  - 半透明绿色背景 `background: rgba(16, 185, 129, 0.1)`
  - 持久显示直到取消选择

### 2.4 退出选择模式的方式
1. **再次点击聊天按钮** - 退出选择，保持聊天面板打开
2. **按ESC键** - 退出选择模式
3. **点击工具栏外区域** - 退出选择模式
4. **发送消息** - 自动退出选择模式
5. **点击其他工具栏按钮** - 退出选择模式

### 2.5 元素信息提取
每个选中的元素提取以下信息:
```typescript
interface SelectedElement {
  id: string                    // 元素ID或生成的唯一ID
  tagName: string              // HTML标签名（小写）
  className: string            // CSS类名
  xpath: string                // XPath路径
  boundingRect: DOMRect        // 边界矩形信息
  textContent: string          // 文本内容（trim后）
  element: HTMLElement         // DOM元素引用
  timestamp: number            // 选择时间戳
}
```

### 2.6 智能排除规则
自动排除以下元素，避免选择到工具组件:
- `.snapmark-toolbar` - 工具栏本身
- `[data-snapmark]` - 标记的工具组件  
- `.dock` - dock组件
- `body`, `html` - 页面根元素
- `script`, `style` - 脚本和样式标签

## 3. 聊天面板功能

### 3.1 面板结构
```
┌─────────────────────────────┐
│ 📍 Agent状态指示器           │
├─────────────────────────────┤
│ 🏷️ 选中元素Chips展示区      │
├─────────────────────────────┤
│ 💬 消息历史显示区           │
├─────────────────────────────┤
│ ✏️ 文本输入框              │
│ [Send按钮]                 │
└─────────────────────────────┘
```

### 3.2 选中元素Chips
- **显示格式**: `<tagName>.className` 或 `<tagName>#id`
- **样式**: 圆角矩形，浅蓝色背景，深蓝色文字
- **交互**: 
  - 悬停显示完整XPath
  - 点击X删除该元素
  - 点击chip高亮对应DOM元素

### 3.3 文本输入区
- **占位符**: "Describe what you want to do with the selected elements..."
- **多行支持**: 自动调整高度
- **快捷键**: 
  - `Enter` - 发送消息
  - `Shift + Enter` - 换行
  - `Escape` - 退出选择模式

### 3.4 Agent状态显示
- **IDLE**: 无状态显示
- **THINKING**: 紫色旋转加载图标
- **WORKING**: 蓝色旋转加载图标  
- **CALLING_TOOL**: 紫红色旋转齿轮图标
- **COMPLETED**: 绿色对勾图标
- **FAILED**: 红色X图标

## 4. 设置面板功能

### 4.1 面板内容
- **Agent连接状态**
  - 当前连接的Agent名称
  - 连接状态指示器
  - 重新连接按钮

- **配置选项**
  - 启用/禁用插件开关
  - 选择行为设置
  - 快捷键配置

- **帮助链接**
  - 使用文档
  - 社区支持
  - 版本信息

### 4.2 面板定位
- **相对工具栏位置**: 自动调整展开方向
- **上半屏**: 向下展开
- **下半屏**: 向上展开
- **左侧**: 左对齐
- **右侧**: 右对齐

## 5. 工具栏交互和状态管理

### 5.1 状态管理架构
```typescript
// 工具栏基础状态
interface ToolbarState {
  isMinimized: boolean          // 最小化状态
  position: { x: number, y: number }  // 拖拽位置
  isDragging: boolean           // 拖拽状态
}

// 面板状态
interface PanelState {
  isSettingsOpen: boolean       // 设置面板
  isChatOpen: boolean           // 聊天面板
  isSelectionMode: boolean      // 元素选择模式
}

// 选择状态
interface SelectionState {
  hoveredElement: HTMLElement | null     // 当前悬停元素
  selectedElements: SelectedElement[]    // 选中元素列表
  selectionHistory: SelectedElement[][]  // 选择历史
}

// 聊天状态
interface ChatState {
  input: string                 // 输入文本
  messages: ChatMessage[]       // 消息历史
  agentStatus: AgentStatus      // Agent状态
  isSending: boolean           // 发送状态
}
```

### 5.2 持久化存储
- **LocalStorage**:
  - `snapmark-toolbar-position` - 工具栏位置
  - `snapmark-toolbar-minimized` - 最小化状态
  - `snapmark-settings` - 用户设置

- **SessionStorage**:
  - `snapmark-selected-elements` - 当前会话选中元素
  - `snapmark-chat-history` - 聊天历史

### 5.3 拖拽功能
- **拖拽区域**: 整个工具栏可拖拽
- **捕捉区域**: 屏幕四个角落自动吸附
- **边界限制**: 防止拖出可视区域
- **位置记忆**: 自动保存和恢复位置
- **视觉反馈**: 拖拽时缩放 + 阴影效果

## 6. 键盘快捷键

### 6.1 全局快捷键
- `Escape` - 退出选择模式
- `Ctrl/Cmd + K` - 打开聊天面板
- `Ctrl/Cmd + ,` - 打开设置面板
- `Ctrl/Cmd + M` - 最小化/展开工具栏

### 6.2 选择模式快捷键
- `Delete/Backspace` - 删除最后选中的元素
- `Ctrl/Cmd + A` - 选择所有可选元素（谨慎使用）
- `Ctrl/Cmd + Z` - 撤销最后一次选择

### 6.3 聊天面板快捷键
- `Enter` - 发送消息
- `Shift + Enter` - 换行
- `Ctrl/Cmd + Enter` - 强制发送
- `Up/Down` - 浏览输入历史

## 7. 错误处理和用户反馈

### 7.1 错误情况处理
- **Agent连接失败**: 显示重连提示
- **元素选择失败**: 温和提示用户重试
- **消息发送失败**: 提供重发选项
- **工具栏拖拽越界**: 自动回弹到安全位置

### 7.2 用户反馈机制
- **Toast通知**: 成功/失败操作反馈
- **加载指示器**: 长时间操作的进度显示
- **状态图标**: 实时显示系统状态
- **动画效果**: 平滑的状态转换

### 7.3 性能优化
- **防抖处理**: 鼠标移动事件防抖
- **内存清理**: 及时清理DOM引用
- **事件监听器管理**: 合理添加和移除监听器
- **渲染优化**: 避免不必要的重渲染

## 8. 移动端适配（可选）

### 8.1 响应式设计
- **触摸支持**: 支持触摸拖拽和选择
- **按钮大小**: 适配触摸操作的最小尺寸
- **面板布局**: 移动端下的布局调整

### 8.2 手势支持
- **长按**: 开始选择模式
- **双击**: 快速选择元素
- **滑动**: 切换工具栏状态

## 9. 可访问性支持

### 9.1 键盘导航
- **Tab键**: 在工具栏按钮间切换
- **Space/Enter**: 激活按钮
- **方向键**: 在选中元素间导航

### 9.2 屏幕阅读器支持
- **ARIA标签**: 为所有交互元素添加语义标签
- **状态通知**: 重要状态变化的语音反馈
- **焦点管理**: 合理的焦点移动顺序

## 10. 插件扩展接口（高级功能）

### 10.1 插件生命周期钩子
```typescript
interface SnapmarkPlugin {
  name: string
  onSelectionStart?(): void           // 选择开始时
  onSelectionEnd?(): void             // 选择结束时  
  onElementSelect?(element: SelectedElement): void  // 元素选中时
  onContextExtract?(element: HTMLElement): any     // 上下文提取
  onMessageSend?(message: string, context: any): void  // 消息发送时
}
```

### 10.2 上下文增强
- 插件可以为选中元素添加额外上下文信息
- 支持框架特定的组件信息提取（React、Vue等）
- 可扩展的元素属性分析

---

## 实现优先级

### Phase 1 (核心功能)
1. ✅ 基础工具栏UI和拖拽功能
2. ✅ DOM元素选择器实现
3. 🔄 聊天面板UI组件
4. 🔄 选中元素chips显示
5. 🔄 选择模式退出机制

### Phase 2 (完善交互)
6. ⏳ 设置面板实现
7. ⏳ 键盘快捷键支持
8. ⏳ 错误处理和用户反馈
9. ⏳ 数据持久化

### Phase 3 (高级功能)
10. ⏳ Agent集成接口
11. ⏳ 插件系统支持
12. ⏳ 移动端适配
13. ⏳ 可访问性优化

这份功能清单基于对stagewise源代码的深度分析，确保了与原版功能的完全对标，可作为Snapmark项目的完整开发指南。