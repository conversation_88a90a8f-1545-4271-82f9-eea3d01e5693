@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Spring easing curves for smooth animations */
    --ease-spring: linear(0.00, -0.00946, 0.0219, 0.0793, 0.152, 0.237, 0.326, 0.419, 0.509, 0.597, 0.680, 0.755, 0.823, 0.882, 0.933, 0.976, 1.01, 1.04, 1.06, 1.07, 1.08, 1.09, 1.09, 1.09, 1.09, 1.08, 1.07, 1.07, 1.06, 1.05, 1.04, 1.03, 1.03, 1.02, 1.01, 1.01, 1.00, 1.00, 0.998, 0.996, 0.994, 0.993, 0.992, 0.992, 0.992, 0.992, 0.992, 0.993, 0.994, 0.994, 0.995, 0.996, 0.997, 0.997, 0.998, 0.998, 0.999, 0.999, 1.00);
    --ease-spring-soft: linear(0.00, -0.0531, 0.117, 0.293, 0.460, 0.610, 0.738, 0.838, 0.913, 0.965, 1.00, 1.02, 1.03, 1.03, 1.03, 1.03, 1.03, 1.02, 1.01, 1.01, 1.01, 1.00, 1.00, 1.00, 1.00);
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}