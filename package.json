{"name": "snapmark", "displayName": "__MSG_extensionName__", "version": "0.0.1", "description": "__MSG_extensionDescription__", "author": "<EMAIL>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.534.0", "motion": "^12.23.12", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "3.2.4", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*/*"], "permissions": ["storage"], "default_locale": "en", "options_ui": {"page": "options.html", "open_in_tab": true}}}