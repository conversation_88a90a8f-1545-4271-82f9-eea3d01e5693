import React, { useState, useEffect } from "react"
import { Switch } from "~components/ui/switch"
import { Label } from "~components/ui/label"
import { ConfigManager } from "~lib/storage"
import "./style.css"
import DockToolbar from "~components/toolbar/dock-toolbar"

function SnapmarkPopup() {
  const [isEnabled, setIsEnabled] = useState(false)
  const [loading, setLoading] = useState(true)

  // Load initial state
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const config = await ConfigManager.getConfig()
        setIsEnabled(config.enabled)
        setLoading(false)
      } catch (error) {
        console.error('Failed to load config:', error)
        setLoading(false)
      }
    }
    loadConfig()
  }, [])

  const handleToggle = async (enabled: boolean) => {
    try {
      await ConfigManager.setConfig({ enabled })
      setIsEnabled(enabled)
    } catch (error) {
      console.error('Failed to save config:', error)
    }
  }

  if (loading) {
    return (
      <div className="w-80 p-4 bg-white text-gray-900 min-h-[120px] flex items-center justify-center">
        <div className="text-sm text-gray-500">Loading...</div>
      </div>
    )
  }

  return (
    <div className="w-80 p-4 bg-white text-gray-900">
      <div className="space-y-4">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-lg font-semibold text-gray-800">Snapmark</h1>
          <p className="text-sm text-gray-500 mt-1">
            AI Element Identification
          </p>
        </div>

        {/* Main Toggle */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex-1">
            <Label htmlFor="enable-snapmark" className="text-sm font-medium text-gray-700">
              Enable Extension
            </Label>
            <p className="text-xs text-gray-500 mt-0.5">
              Show toolbar on web pages
            </p>
          </div>
          <Switch
            id="enable-snapmark"
            checked={isEnabled}
            onCheckedChange={handleToggle}
            className="ml-3"
          />
        </div>

        {/* Status */}
        <div className="flex items-center justify-center gap-2 text-sm">
          <div className={`w-2 h-2 rounded-full ${
            isEnabled ? 'bg-green-500' : 'bg-gray-400'
          }`} />
          <span className="text-gray-600">
            {isEnabled ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>
    </div>
  )
}

export default SnapmarkPopup
