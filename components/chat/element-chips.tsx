import React, { useState } from 'react';
import { X, Eye, Info } from 'lucide-react';
import { cn } from '../../lib/utils';
import { getElementDisplayName, scrollElementIntoView } from '../../lib/dom-utils';
import { Button } from '../ui/button';

export interface ElementChip {
  id: string;
  element: HTMLElement;
  displayName: string;
  xpath: string;
}

export interface ElementChipsProps {
  elements: HTMLElement[];
  onRemoveElement: (element: HTMLElement) => void;
  onClearAll: () => void;
  onElementHover?: (element: HTMLElement | null) => void;
  className?: string;
}

export function ElementChips({
  elements,
  onRemoveElement,
  onClearAll,
  onElementHover,
  className
}: ElementChipsProps) {
  const [hoveredChip, setHoveredChip] = useState<HTMLElement | null>(null);

  if (elements.length === 0) {
    return null;
  }

  const handleChipClick = (element: HTMLElement) => {
    // Scroll element into view and highlight it briefly
    scrollElementIntoView(element);
    
    // Add temporary highlight
    const originalOutline = element.style.outline;
    const originalBoxShadow = element.style.boxShadow;
    
    element.style.outline = '3px solid #3b82f6';
    element.style.boxShadow = '0 0 0 6px rgba(59, 130, 246, 0.3)';
    
    setTimeout(() => {
      element.style.outline = originalOutline;
      element.style.boxShadow = originalBoxShadow;
    }, 2000);
  };

  const handleChipHover = (element: HTMLElement | null) => {
    setHoveredChip(element);
    onElementHover?.(element);
  };

  const handleRemoveClick = (event: React.MouseEvent, element: HTMLElement) => {
    event.stopPropagation();
    onRemoveElement(element);
  };

  return (
    <div className={cn("space-y-2", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">
            Selected Elements ({elements.length})
          </span>
          <Info className="w-4 h-4 text-gray-400" title="Click chips to highlight elements" />
        </div>
        {elements.length > 1 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearAll}
            className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Chips Container */}
      <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
        {elements.map((element) => {
          const displayName = getElementDisplayName(element);
          const isHovered = hoveredChip === element;
          
          return (
            <div
              key={element.id || displayName}
              className={cn(
                "group flex items-center gap-1 px-2 py-1 rounded-md border transition-all duration-200 cursor-pointer",
                "bg-blue-50 border-blue-200 text-blue-800 hover:bg-blue-100 hover:border-blue-300",
                isHovered && "bg-blue-100 border-blue-300 shadow-sm"
              )}
              onClick={() => handleChipClick(element)}
              onMouseEnter={() => handleChipHover(element)}
              onMouseLeave={() => handleChipHover(null)}
              title={`XPath: ${element.getAttribute('data-xpath') || 'N/A'}\nClick to highlight, X to remove`}
            >
              {/* Element icon */}
              <div className="w-3 h-3 rounded-sm bg-blue-600 flex items-center justify-center">
                <span className="text-white text-xs font-bold">
                  {element.tagName.charAt(0)}
                </span>
              </div>
              
              {/* Element name */}
              <span className="text-xs font-medium max-w-[120px] truncate">
                {displayName}
              </span>
              
              {/* Actions */}
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => handleChipClick(element)}
                  className="p-0.5 rounded hover:bg-blue-200 transition-colors"
                  title="Highlight element"
                >
                  <Eye className="w-3 h-3" />
                </button>
                <button
                  onClick={(e) => handleRemoveClick(e, element)}
                  className="p-0.5 rounded hover:bg-red-200 text-red-600 transition-colors"
                  title="Remove element"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary */}
      <div className="text-xs text-gray-500 border-t pt-2">
        {elements.length === 1 
          ? "1 element selected" 
          : `${elements.length} elements selected`}
        {" • Click chips to highlight • Hover to preview"}
      </div>
    </div>
  );
}
