"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Send, X, Loader2, Check<PERSON>ircle, XCircle, AlertCircle, Settings } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import type { SelectedElement } from '../../hooks/use-element-selector';
import { ElementChips } from './element-chips';

export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: number;
  context?: SelectedElement[];
}

export type AgentStatus = 'idle' | 'thinking' | 'working' | 'calling_tool' | 'completed' | 'failed';

interface ChatPanelProps {
  isOpen: boolean;
  selectedElements: SelectedElement[];
  agentStatus: AgentStatus;
  messages: ChatMessage[];
  onSendMessage: (message: string, context: SelectedElement[]) => void;
  onRemoveElement: (elementId: string) => void;
  onClearElements: () => void;
  onElementHover?: (element: HTMLElement | null) => void;
  onClose: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export default function ChatPanel({
  isOpen,
  selectedElements,
  agentStatus,
  messages,
  onSendMessage,
  onRemoveElement,
  onClearElements,
  onElementHover,
  onClose,
  className,
  style
}: ChatPanelProps) {
  const [input, setInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Anti-flickering: delayed rendering state
  const [shouldRender, setShouldRender] = useState(isOpen);
  const stopRenderTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    if (!isOpen) {
      // Wait for exit animation to complete before removing from DOM
      stopRenderTimeoutRef.current = setTimeout(() => {
        setShouldRender(false);
      }, 500); // Match stagewise timing
    } else {
      setShouldRender(true);
      if (stopRenderTimeoutRef.current) {
        clearTimeout(stopRenderTimeoutRef.current);
        stopRenderTimeoutRef.current = null;
      }
    }
  }, [isOpen]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (stopRenderTimeoutRef.current) {
        clearTimeout(stopRenderTimeoutRef.current);
      }
    };
  }, []);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [input]);

  // Handle send message
  const handleSend = useCallback(async () => {
    if (!input.trim() || isSending) return;

    const message = input.trim();
    setInput('');
    setIsSending(true);

    try {
      await onSendMessage(message, selectedElements);
    } finally {
      setIsSending(false);
    }
  }, [input, isSending, selectedElements, onSendMessage]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  // Get agent status icon and color
  const getAgentStatusDisplay = () => {
    switch (agentStatus) {
      case 'thinking':
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin text-purple-500" />,
          text: 'Thinking...',
          color: 'text-purple-500'
        };
      case 'working':
        return {
          icon: <Loader2 className="w-4 h-4 animate-spin text-blue-500" />,
          text: 'Working...',
          color: 'text-blue-500'
        };
      case 'calling_tool':
        return {
          icon: <Settings className="w-4 h-4 animate-spin text-pink-500" />,
          text: 'Using tools...',
          color: 'text-pink-500'
        };
      case 'completed':
        return {
          icon: <CheckCircle className="w-4 h-4 text-green-500" />,
          text: 'Completed',
          color: 'text-green-500'
        };
      case 'failed':
        return {
          icon: <XCircle className="w-4 h-4 text-red-500" />,
          text: 'Failed',
          color: 'text-red-500'
        };
      default:
        return null;
    }
  };



  return (
    <div
      className={cn(
        "fixed z-[9998] w-80 h-auto transition-all duration-500 ease-spring overflow-hidden",
        isOpen 
          ? "" 
          : "h-0 scale-0 opacity-0 blur-md",
        className
      )}
      style={style}
    >
      {shouldRender && (
        <div
          className={cn(
            "bg-white/95 backdrop-blur-md border border-white/20",
            "rounded-lg shadow-xl overflow-hidden",
            "flex flex-col max-h-96"
          )}
          data-snapmark="snapmark-chat-panel"
        >
          {/* Header with Agent Status */}
          <div className="flex items-center justify-between p-3 border-b border-white/20 bg-white/50">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-800">Snapmark Chat</span>
              {getAgentStatusDisplay() && (
                <div className="flex items-center gap-1">
                  {getAgentStatusDisplay()!.icon}
                  <span className={cn("text-xs", getAgentStatusDisplay()!.color)}>
                    {getAgentStatusDisplay()!.text}
                  </span>
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-6 h-6 p-0 hover:bg-white/20"
              onClick={onClose}
            >
              <X className="w-3 h-3" />
            </Button>
          </div>

          {/* Selected Elements Chips */}
          {selectedElements.length > 0 && (
            <div className="p-3 border-b border-white/20 bg-blue-50/50">
              <ElementChips
                elements={selectedElements.map(el => el.element)}
                onRemoveElement={(element) => {
                  const selectedElement = selectedElements.find(sel => sel.element === element);
                  if (selectedElement) {
                    onRemoveElement(selectedElement.id);
                  }
                }}
                onClearAll={onClearElements}
                onElementHover={onElementHover}
              />
            </div>
          )}

          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-3 space-y-2 min-h-[120px] max-h-[200px]">
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 text-sm py-4">
                Select elements and describe what you want to do
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "p-2 rounded-lg text-sm",
                    message.sender === 'user'
                      ? "bg-blue-100 text-blue-900 ml-4"
                      : "bg-gray-100 text-gray-900 mr-4"
                  )}
                >
                  <div>{message.content}</div>
                  {message.context && message.context.length > 0 && (
                    <div className="mt-1 text-xs opacity-70">
                      Context: {message.context.length} element(s)
                    </div>
                  )}
                  <div className="text-xs opacity-50 mt-1">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-3 border-t border-white/20 bg-white/50">
            <div className="flex gap-2">
              <textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={
                  selectedElements.length > 0
                    ? "Describe what you want to do with the selected elements..."
                    : "First select some elements, then describe what you want to do..."
                }
                className="flex-1 resize-none bg-white/80 border border-white/30 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                rows={1}
                disabled={isSending}
              />
              <Button
                onClick={handleSend}
                disabled={!input.trim() || isSending}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white px-3"
              >
                {isSending ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            </div>
            
            {/* Shortcuts Help */}
            <div className="mt-2 text-xs text-gray-500">
              <span>Press Enter to send, Shift+Enter for new line, Esc to exit selection</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
