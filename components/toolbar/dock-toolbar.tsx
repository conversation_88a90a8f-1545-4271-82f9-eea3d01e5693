"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";

import { Dock, DockIcon } from "../magicui/dock";
import { useElementSelector } from "../../hooks/use-element-selector";
import { useChat } from "../../hooks/use-chat";
import ChatPanel from "../chat/chat-panel";
import { DOMSelector } from "../dom-selector/selector-canvas";
import SettingsPanel from "../panels/settings-panel";

export type IconProps = React.HTMLAttributes<SVGElement>;

interface DockToolbarProps {
  onOpenSettings?: () => void;
  onMessageSend?: (message: string, context: any[]) => Promise<void>;
  onMinimize?: () => void;
  isMinimized?: boolean;
}

export default function DockToolbar({
  onOpenSettings,
  onMessageSend,
  onMinimize,
  isMinimized = false
}: DockToolbarProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [settingsOpen, setSettingsOpen] = useState(false);
  const toolbarRef = useRef<HTMLDivElement>(null);

  // Chat functionality hook
  const {
    isChatOpen,
    messages,
    agentStatus,
    isSelectionMode,
    toggleChat,
    exitSelectionMode,
    sendMessage,
    clearMessages
  } = useChat({
    onMessageSend,
    onSelectionModeChange: (isActive) => {
      console.log('Selection mode changed:', isActive);
    }
  });

  // Element selector hook (stagewise approach - no excludeSelectors needed)
  const {
    isSelecting,
    selectedElements,
    toggleSelection,
    clearSelectedElements,
    removeSelectedElement
  } = useElementSelector({
    onElementSelect: (element) => {
      console.log('Element selected:', element);
    }
  });

  // Sync selection mode between chat and element selector
  useEffect(() => {
    if (isSelectionMode && !isSelecting) {
      toggleSelection();
    } else if (!isSelectionMode && isSelecting) {
      toggleSelection();
    }
  }, [isSelectionMode, isSelecting, toggleSelection]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!toolbarRef.current) return;
    
    setIsDragging(true);
    const rect = toolbarRef.current.getBoundingClientRect();
    setDragStart({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    e.preventDefault();
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;
    
    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;
    
    const maxX = window.innerWidth - 200;
    const maxY = window.innerHeight - 80;
    
    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Calculate chat panel position relative to toolbar
  const getChatPanelPosition = () => {
    if (!toolbarRef.current) return {};
    
    const rect = toolbarRef.current.getBoundingClientRect();
    const panelWidth = 320; // Chat panel width
    const panelHeight = 400; // Chat panel max height
    const spacing = 8; // Spacing between toolbar and panel
    
    // Determine if toolbar is in top or bottom half of screen
    const isTopHalf = rect.top < window.innerHeight / 2;
    // Determine if toolbar is in left or right half of screen  
    const isLeftHalf = rect.left < window.innerWidth / 2;
    
    let top, left;
    
    if (isTopHalf) {
      // Position below toolbar
      top = rect.bottom + spacing;
    } else {
      // Position above toolbar
      top = rect.top - panelHeight - spacing;
    }
    
    if (isLeftHalf) {
      // Align to left edge of toolbar
      left = rect.left;
    } else {
      // Align to right edge of toolbar
      left = rect.right - panelWidth;
    }
    
    // Ensure panel stays within viewport
    left = Math.max(8, Math.min(left, window.innerWidth - panelWidth - 8));
    top = Math.max(8, Math.min(top, window.innerHeight - panelHeight - 8));
    
    return { top, left };
  };

  // Load position from localStorage
  useEffect(() => {
    const stored = localStorage.getItem('snapmark-dock-position');
    if (stored) {
      try {
        setPosition(JSON.parse(stored));
      } catch (e) {
        console.warn('Failed to load dock position');
      }
    }
  }, []);

  // Save position when changed
  useEffect(() => {
    if (position.x !== 0 || position.y !== 0) {
      localStorage.setItem('snapmark-dock-position', JSON.stringify(position));
    }
  }, [position]);

  // Handle settings button click
  const handleSettingsClick = () => {
    setSettingsOpen(!settingsOpen);
    onOpenSettings?.();
  };

  // Handle chat button click (dual functionality)
  const handleChatClick = () => {
    console.log('Chat button clicked, current state:', { isChatOpen, isSelectionMode });
    toggleChat();
  };

  // Handle minimize button click
  const handleMinimizeClick = () => {
    onMinimize?.();
  };

  // If minimized, show only a small circle
  if (isMinimized) {
    return (
      <div 
        ref={toolbarRef}
        className={`snapmark-toolbar fixed z-[9999] w-12 h-12 rounded-full cursor-move select-none transition-transform bg-white/80 backdrop-blur-md border border-white/20 shadow-lg hover:scale-105 ${
          isDragging ? 'scale-110 shadow-2xl' : ''
        }`}
        data-snapmark="dock-toolbar-minimized"
        style={{ 
          right: position.x === 0 ? '1rem' : 'auto',
          bottom: position.y === 0 ? '1rem' : 'auto',
          left: position.x > 0 ? `${position.x}px` : 'auto',
          top: position.y > 0 ? `${position.y}px` : 'auto'
        }}
        onMouseDown={handleMouseDown}
        onClick={handleMinimizeClick}
      >
        <div className="w-full h-full flex items-center justify-center">
          <Icons.maximize className="w-5 h-5 text-gray-700" />
        </div>
      </div>
    );
  }
  return (
    <div 
      ref={toolbarRef}
      className={`snapmark-toolbar fixed z-[9999] cursor-move select-none transition-transform ${
        isDragging ? 'scale-105 shadow-2xl' : 'hover:scale-105'
      }`}
      data-snapmark="snapmark-toolbar"
      style={{ 
        right: position.x === 0 ? '1rem' : 'auto',
        bottom: position.y === 0 ? '1rem' : 'auto',
        left: position.x > 0 ? `${position.x}px` : 'auto',
        top: position.y > 0 ? `${position.y}px` : 'auto'
      }}
      onMouseDown={handleMouseDown}
    >
      <Dock direction="middle">
        <DockIcon 
          className={`bg-black/10 dark:bg-white/10 hover:bg-blue-500/20 transition-colors cursor-pointer ${
            settingsOpen ? 'bg-blue-500/30' : ''
          }`}
          onClick={handleSettingsClick}
        >
          <Icons.settings className="size-full" />
        </DockIcon>
        <DockIcon 
          className={`bg-black/10 dark:bg-white/10 hover:bg-blue-500/20 transition-colors cursor-pointer ${
            isChatOpen ? 'bg-green-500/30' : ''
          } ${isSelectionMode ? 'animate-pulse' : ''}`}
          onClick={handleChatClick}
        >
          <Icons.messageCircle className="size-full" />
        </DockIcon>
        <DockIcon 
          className="bg-black/10 dark:bg-white/10 hover:bg-blue-500/20 transition-colors cursor-pointer"
          onClick={handleMinimizeClick}
        >
          <Icons.minimize className="size-full" />
        </DockIcon>
      </Dock>
      
      {/* Chat Panel */}
      <ChatPanel
        isOpen={isChatOpen}
        selectedElements={selectedElements}
        agentStatus={agentStatus}
        messages={messages}
        onSendMessage={sendMessage}
        onRemoveElement={removeSelectedElement}
        onClearElements={clearSelectedElements}
        onClose={() => {
          // Close chat but keep any selected elements
          toggleChat();
        }}
        className="chat-panel"
        style={getChatPanelPosition()}
      />

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        style={getChatPanelPosition()} // Use same positioning logic
      />

      {/* DOM Selector Overlay */}
      <DOMSelector
        isActive={isSelectionMode}
        selectedElements={selectedElements.map(el => el.element)}
        onElementSelect={(element) => {
          // This will be handled by the useElementSelector hook
          console.log('Element selected via DOM selector:', element);
        }}
        onElementRemove={(element) => {
          const selectedElement = selectedElements.find(sel => sel.element === element);
          if (selectedElement) {
            removeSelectedElement(selectedElement);
          }
        }}
      />
    </div>
  );
}

const Icons = {
  settings: (props: IconProps) => (
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" {...props}>
      <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  ),
  messageCircle: (props: IconProps) => (
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" {...props}>
      <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"/>
    </svg>
  ),
  minimize: (props: IconProps) => (
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" {...props}>
      <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
    </svg>
  ),
  maximize: (props: IconProps) => (
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" {...props}>
      <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
      <path d="m9 9 3 3 3-3"/>
    </svg>
  ),
};
