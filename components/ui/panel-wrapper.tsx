"use client";

import { useState, useEffect, useRef } from 'react';
import { cn } from '../../lib/utils';

interface PanelWrapperProps {
  children: React.ReactNode;
  isOpen: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export function PanelWrapper({
  children,
  isOpen,
  className,
  style
}: PanelWrapperProps) {
  // Delayed rendering to prevent flickering during animations
  const [shouldRender, setShouldRender] = useState(isOpen);
  const stopRenderTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!isOpen) {
      // Wait for exit animation to complete before removing from DOM
      stopRenderTimeoutRef.current = setTimeout(() => {
        setShouldRender(false);
      }, 150); // Match animation duration
    } else {
      setShouldRender(true);
      if (stopRenderTimeoutRef.current) {
        clearTimeout(stopRenderTimeoutRef.current);
        stopRenderTimeoutRef.current = null;
      }
    }
  }, [isOpen]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (stopRenderTimeoutRef.current) {
        clearTimeout(stopRenderTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      className={cn(
        'h-auto transition-all duration-150 ease-out overflow-hidden',
        isOpen 
          ? '' 
          : 'h-0 scale-0 opacity-0 blur-md',
        className
      )}
      style={style}
    >
      {shouldRender && children}
    </div>
  );
}