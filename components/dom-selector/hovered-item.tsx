import React, { useCallback, useRef, useEffect } from 'react';
import type { HTMLAttributes } from 'react';
import { cn } from '../../lib/utils';

export interface HoveredItemProps extends HTMLAttributes<HTMLDivElement> {
  refElement: HTMLElement;
}

export function HoveredItem({ refElement, ...props }: HoveredItemProps) {
  const boxRef = useRef<HTMLDivElement>(null);

  const updateBoxPosition = useCallback(() => {
    if (boxRef.current && refElement) {
      const referenceRect = refElement.getBoundingClientRect();

      boxRef.current.style.top = `${referenceRect.top - 2}px`;
      boxRef.current.style.left = `${referenceRect.left - 2}px`;
      boxRef.current.style.width = `${referenceRect.width + 4}px`;
      boxRef.current.style.height = `${referenceRect.height + 4}px`;
      boxRef.current.style.display = 'block';
    } else if (boxRef.current) {
      boxRef.current.style.display = 'none';
    }
  }, [refElement]);

  // Update position on every animation frame for smooth tracking
  useEffect(() => {
    let animationFrameId: number;
    
    const updateLoop = () => {
      updateBoxPosition();
      animationFrameId = requestAnimationFrame(updateLoop);
    };
    
    updateLoop();
    
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [updateBoxPosition]);

  const getElementDisplayName = (element: HTMLElement): string => {
    const tagName = element.tagName.toLowerCase();
    const className = element.className;
    const id = element.id;
    
    if (id) {
      return `${tagName}#${id}`;
    } else if (className && typeof className === 'string') {
      const firstClass = className.split(' ')[0];
      return `${tagName}.${firstClass}`;
    }
    return tagName;
  };

  return (
    <div
      {...props}
      className={cn(
        'fixed z-[2147483647] flex items-center justify-center rounded-sm border-2 border-blue-600/70 border-dotted bg-blue-600/5 text-white transition-all duration-100 pointer-events-none',
      )}
      ref={boxRef}
      data-snapmark="hovered-element-overlay"
    >
      <div className="absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1">
        <div className="flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/90 px-2 py-1 font-medium text-white text-xs shadow-lg">
          <span className="truncate max-w-[200px]">
            {getElementDisplayName(refElement)}
          </span>
        </div>
      </div>
    </div>
  );
}
