import React, { useCallback, useRef, useEffect } from 'react';
import type { HTMLAttributes } from 'react';
import { cn } from '../../lib/utils';
import { X } from 'lucide-react';

export interface SelectedItemProps extends HTMLAttributes<HTMLButtonElement> {
  refElement: HTMLElement;
  isChipHovered?: boolean;
  onRemoveClick: () => void;
}

export function SelectedItem({
  refElement,
  isChipHovered = false,
  onRemoveClick,
  ...props
}: SelectedItemProps) {
  const boxRef = useRef<HTMLButtonElement>(null);

  const updateBoxPosition = useCallback(() => {
    if (boxRef.current && refElement) {
      const referenceRect = refElement.getBoundingClientRect();

      boxRef.current.style.top = `${referenceRect.top - 2}px`;
      boxRef.current.style.left = `${referenceRect.left - 2}px`;
      boxRef.current.style.width = `${referenceRect.width + 4}px`;
      boxRef.current.style.height = `${referenceRect.height + 4}px`;
      boxRef.current.style.display = 'block';
    } else if (boxRef.current) {
      boxRef.current.style.display = 'none';
    }
  }, [refElement]);

  // Update position on every animation frame for smooth tracking
  useEffect(() => {
    let animationFrameId: number;
    
    const updateLoop = () => {
      updateBoxPosition();
      animationFrameId = requestAnimationFrame(updateLoop);
    };
    
    updateLoop();
    
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [updateBoxPosition]);

  const handleClick = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    onRemoveClick();
  }, [onRemoveClick]);

  return (
    <button
      {...props}
      className={cn(
        'pointer-events-auto fixed flex cursor-pointer items-center justify-center rounded-sm border-2 border-green-600/70 border-solid bg-green-600/10 transition-all duration-100 hover:border-red-600/70 hover:bg-red-600/10 group',
        isChipHovered && 'border-blue-600/70 bg-blue-600/10',
      )}
      onClick={handleClick}
      ref={boxRef}
      data-snapmark="selected-element-overlay"
      title="Click to remove this element"
    >
      {/* Remove button - only show on hover */}
      <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div className="bg-red-600 text-white rounded-full p-1 shadow-lg">
          <X className="w-3 h-3" />
        </div>
      </div>
      
      {/* Element info badge */}
      <div className="absolute bottom-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div className="bg-green-700/90 text-white rounded-md px-2 py-1 text-xs font-medium shadow-lg">
          Selected
        </div>
      </div>
    </button>
  );
}
