import React, {
  type Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { HoveredItem } from './hovered-item';
import { SelectedItem } from './selected-item';
import { getElementAtPoint, getXPathForElement } from '../../lib/dom-utils';
import { cn } from '../../lib/utils';

export interface DOMSelectorProps {
  isActive: boolean;
  selectedElements: HTMLElement[];
  onElementSelect: (element: HTMLElement) => void;
  onElementRemove: (element: HTMLElement) => void;
  hoveredElement?: HTMLElement | null;
  onHoveredElementChange?: (element: HTMLElement | null) => void;
}

export function DOMSelector({
  isActive,
  selectedElements,
  onElementSelect,
  onElementRemove,
  hoveredElement: externalHoveredElement,
  onHoveredElementChange,
}: DOMSelectorProps) {
  const [internalHoveredElement, setInternalHoveredElement] = useState<HTMLElement | null>(null);
  
  // Use external hovered element if provided, otherwise use internal state
  const hoveredElement = externalHoveredElement !== undefined 
    ? externalHoveredElement 
    : internalHoveredElement;
    
  const setHoveredElement = onHoveredElementChange || setInternalHoveredElement;

  const handleElementSelected = useCallback(
    (el: HTMLElement) => {
      // Check if element is already selected
      const isAlreadySelected = selectedElements.includes(el);

      if (isAlreadySelected) {
        // If already selected, remove it
        onElementRemove(el);
      } else {
        // If not selected, add it
        onElementSelect(el);
      }
    },
    [onElementSelect, onElementRemove, selectedElements],
  );

  // Check if the hovered element is already selected
  const hoveredSelectedElement = hoveredElement
    ? selectedElements.find((el) => el === hoveredElement)
    : null;

  const lastHoveredElement = useRef<HTMLElement | null>(null);
  const mouseState = useRef<{
    lastX: number;
    lastY: number;
    velocity: number;
    lastTimestamp: number;
  } | null>(null);

  const nextUpdateTimeout = useRef<NodeJS.Timeout | null>(null);

  const [hoversAddable, setHoversAddable] = useState(false);

  const updateHoveredElement = useCallback(() => {
    if (!mouseState.current) return;
    const refElement = getElementAtPoint(
      mouseState.current.lastX,
      mouseState.current.lastY,
    );
    
    if (selectedElements.includes(refElement)) {
      setHoversAddable(false);
      lastHoveredElement.current = null;
      setHoveredElement(null);
      return;
    }
    
    if (lastHoveredElement.current !== refElement) {
      lastHoveredElement.current = refElement;
      setHoveredElement(refElement);
      setHoversAddable(true);
    }
  }, [selectedElements, setHoveredElement]);

  useEffect(() => {
    updateHoveredElement();
  }, [updateHoveredElement]);

  const handleMouseMove = useCallback<MouseEventHandler<HTMLDivElement>>(
    (event) => {
      // Calculate mouse velocity
      const currentTimestamp = performance.now();

      const deltaX =
        event.clientX - (mouseState.current?.lastX ?? event.clientX);
      const deltaY =
        event.clientY - (mouseState.current?.lastY ?? event.clientY);
      const deltaTime =
        currentTimestamp -
        (mouseState.current?.lastTimestamp ?? currentTimestamp);

      const distance = Math.hypot(deltaX, deltaY);

      mouseState.current = {
        lastX: deltaTime > 0 ? event.clientX : mouseState.current?.lastX ?? event.clientX,
        lastY: deltaTime > 0 ? event.clientY : mouseState.current?.lastY ?? event.clientY,
        velocity: deltaTime > 0 ? (distance / deltaTime) * 1000 : 0,
        lastTimestamp: currentTimestamp,
      };

      // If velocity exceeds 30 pixels per second, delay update
      if (mouseState.current?.velocity > 30) {
        if (nextUpdateTimeout.current) {
          clearTimeout(nextUpdateTimeout.current);
        }
        nextUpdateTimeout.current = setTimeout(updateHoveredElement, 1000 / 28);
      } else if (!nextUpdateTimeout.current) {
        nextUpdateTimeout.current = setTimeout(updateHoveredElement, 1000 / 28);
      }
    },
    [updateHoveredElement],
  );

  const handleMouseLeave = useCallback<
    MouseEventHandler<HTMLDivElement>
  >(() => {
    if (nextUpdateTimeout.current) {
      clearTimeout(nextUpdateTimeout.current);
      nextUpdateTimeout.current = null;
    }
    lastHoveredElement.current = null;
    setHoveredElement(null);
  }, [setHoveredElement]);

  const handleMouseClick = useCallback<MouseEventHandler<HTMLDivElement>>(
    (event) => {
      event.preventDefault();
      event.stopPropagation();

      if (!lastHoveredElement.current) return;
      if (selectedElements.includes(lastHoveredElement.current)) return;

      handleElementSelected(lastHoveredElement.current);
    },
    [handleElementSelected, selectedElements],
  );

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (nextUpdateTimeout.current) {
        clearTimeout(nextUpdateTimeout.current);
      }
    };
  }, []);

  if (!isActive) return null;
  
  return (
    <div
      className={cn(
        'pointer-events-auto fixed inset-0 h-screen w-screen z-[2147483647]',
        hoversAddable ? 'cursor-copy' : 'cursor-default',
      )}
      id="snapmark-element-selector"
      data-snapmark="element-selector"
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={handleMouseClick}
      role="button"
      tabIndex={0}
    >
      {/* Show blue proposal overlay for new elements */}
      {hoveredElement && !hoveredSelectedElement && (
        <HoveredItem refElement={hoveredElement} />
      )}

      {selectedElements.map((element) => (
        <SelectedItem
          key={getXPathForElement(element, true)}
          refElement={element}
          onRemoveClick={() => onElementRemove(element)}
        />
      ))}
    </div>
  );
}
