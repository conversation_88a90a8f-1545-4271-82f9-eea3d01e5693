import React, { useState, useEffect } from 'react';
import { X, Settings, Palette, Keyboard, Download, Info } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import { ConfigManager } from '../../lib/storage';
import type { SnapmarkConfig } from '../../lib/types';

export interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export default function SettingsPanel({
  isOpen,
  onClose,
  className,
  style
}: SettingsPanelProps) {
  const [config, setConfig] = useState<SnapmarkConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load configuration
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const currentConfig = await ConfigManager.getConfig();
        setConfig(currentConfig);
      } catch (error) {
        console.error('Failed to load config:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadConfig();
    }
  }, [isOpen]);

  // Save configuration
  const saveConfig = async (newConfig: SnapmarkConfig) => {
    setIsSaving(true);
    try {
      await ConfigManager.setConfig(newConfig);
      setConfig(newConfig);
    } catch (error) {
      console.error('Failed to save config:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleToggleEnabled = () => {
    if (config) {
      saveConfig({ ...config, enabled: !config.enabled });
    }
  };

  const handleHighlightColorChange = (color: string) => {
    if (config) {
      saveConfig({
        ...config,
        selector: { ...config.selector, highlightColor: color }
      });
    }
  };

  const handleOpacityChange = (opacity: number) => {
    if (config) {
      saveConfig({
        ...config,
        selector: { ...config.selector, highlightOpacity: opacity }
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className={cn(
        "fixed z-[9998] w-80 bg-white/95 backdrop-blur-md border border-white/20 rounded-lg shadow-xl",
        "transition-all duration-300 ease-out",
        className
      )}
      style={style}
      data-snapmark="settings-panel"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/20">
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-800">Settings</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="w-6 h-6 p-0 hover:bg-white/20"
          onClick={onClose}
        >
          <X className="w-3 h-3" />
        </Button>
      </div>

      {/* Content */}
      <div className="p-4 space-y-6 max-h-96 overflow-y-auto">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
            <span className="text-sm text-gray-600">Loading settings...</span>
          </div>
        ) : config ? (
          <>
            {/* General Settings */}
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Settings className="w-4 h-4" />
                General
              </h3>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Enable Extension</span>
                <button
                  onClick={handleToggleEnabled}
                  disabled={isSaving}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    config.enabled ? "bg-blue-600" : "bg-gray-300",
                    isSaving && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      config.enabled ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
              </div>
            </div>

            {/* Selector Settings */}
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Element Selector
              </h3>
              
              <div className="space-y-3">
                <div>
                  <label className="text-sm text-gray-600 block mb-2">Highlight Color</label>
                  <div className="flex gap-2">
                    {['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'].map((color) => (
                      <button
                        key={color}
                        onClick={() => handleHighlightColorChange(color)}
                        className={cn(
                          "w-8 h-8 rounded-full border-2 transition-all",
                          config.selector.highlightColor === color
                            ? "border-gray-800 scale-110"
                            : "border-gray-300 hover:scale-105"
                        )}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>

                <div>
                  <label className="text-sm text-gray-600 block mb-2">
                    Highlight Opacity ({Math.round(config.selector.highlightOpacity * 100)}%)
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={config.selector.highlightOpacity}
                    onChange={(e) => handleOpacityChange(parseFloat(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Show Element Info</span>
                  <button
                    onClick={() => saveConfig({
                      ...config,
                      selector: { ...config.selector, showElementInfo: !config.selector.showElementInfo }
                    })}
                    disabled={isSaving}
                    className={cn(
                      "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                      config.selector.showElementInfo ? "bg-blue-600" : "bg-gray-300",
                      isSaving && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <span
                      className={cn(
                        "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                        config.selector.showElementInfo ? "translate-x-6" : "translate-x-1"
                      )}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* Keyboard Shortcuts */}
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Keyboard className="w-4 h-4" />
                Keyboard Shortcuts
              </h3>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Toggle Selection</span>
                  <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Esc</kbd>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Open Chat</span>
                  <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+K</kbd>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Open Settings</span>
                  <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+,</kbd>
                </div>
              </div>
            </div>

            {/* About */}
            <div className="space-y-3 pt-3 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Info className="w-4 h-4" />
                About
              </h3>
              
              <div className="text-sm text-gray-600 space-y-1">
                <p>Snapmark Extension v1.0.0</p>
                <p>AI-powered DOM element selector</p>
                <p className="text-xs text-gray-500">
                  Inspired by Stagewise toolbar design
                </p>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-red-600">
            Failed to load settings
          </div>
        )}
      </div>
    </div>
  );
}
