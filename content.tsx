import React, { useState, useEffect } from 'react'
import type { PlasmoCSConfig, PlasmoGetOverlayAnchor, PlasmoGetStyle } from 'plasmo'

import DockToolbar from './components/toolbar/dock-toolbar'
import { ConfigManager } from './lib/storage'

// Import styles for content script
import cssText from "data-text:~style.css"

// Plasmo content script configuration
export const config: PlasmoCSConfig = {
  matches: ['https://*/*'],
  all_frames: false,
  run_at: 'document_end'
}

// Plasmo overlay anchor - mount as overlay on top of page
export const getOverlayAnchor: PlasmoGetOverlayAnchor = async () => {
  return document.body
}

// Inject styles for content script
export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement("style")
  style.textContent = cssText.replaceAll(':root', ':host(plasmo-csui)')
  return style
}

const SnapmarkContent: React.FC = () => {
  const [isEnabled, setIsEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [showToolbar, setShowToolbar] = useState(false)

  // Load configuration and initialize
  useEffect(() => {
    const initializeExtension = async () => {
      try {
        const config = await ConfigManager.getConfig()
        setIsEnabled(config.enabled)
        setShowToolbar(config.enabled) // Show toolbar if enabled
      } catch (error) {
        console.error('Failed to initialize Snapmark extension:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeExtension()
  }, [])

  // Listen for configuration changes
  useEffect(() => {
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }, areaName: string) => {
      if (changes.snapmark_config) {
        const newConfig = changes.snapmark_config.newValue
        if (newConfig) {
          setIsEnabled(newConfig.enabled)
          setShowToolbar(newConfig.enabled) // Show/hide toolbar based on enabled state
        }
      }
    }

    chrome.storage.onChanged.addListener(handleStorageChange)

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange)
    }
  }, [])


  // Don't render if loading or toolbar should not be shown
  if (isLoading || !showToolbar) {
    return null
  }

  return (
    <DockToolbar />
  )
}

export default SnapmarkContent
