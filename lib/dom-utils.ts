// DOM utility functions adapted from Stagewise

/**
 * Get element at specific point, excluding toolbar and SVG elements
 */
export function getElementAtPoint(x: number, y: number): HTMLElement {
  const elementsBelowAnnotation = document.elementsFromPoint(x, y);

  const refElement =
    (elementsBelowAnnotation.find(
      (element) =>
        !element.closest('svg') &&
        !element.closest('.snapmark-toolbar') &&
        !element.closest('[data-snapmark]') &&
        !element.closest('#snapmark-element-selector') &&
        isElementAtPoint(element as HTMLElement, x, y),
    ) as HTMLElement) || document.body;

  return refElement;
}

/**
 * Check if element is at specific point
 */
const isElementAtPoint = (
  element: HTMLElement,
  clientX: number,
  clientY: number,
): boolean => {
  const boundingRect = element.getBoundingClientRect();

  const isInHorizontalBounds =
    clientX > boundingRect.left &&
    clientX < boundingRect.left + boundingRect.width;
  const isInVerticalBounds =
    clientY > boundingRect.top &&
    clientY < boundingRect.top + boundingRect.height;

  return isInHorizontalBounds && isInVerticalBounds;
};

/**
 * Get offsets from point to element
 */
export function getOffsetsFromPointToElement(
  refElement: HTMLElement,
  x: number,
  y: number,
) {
  const referenceClientBounds = refElement.getBoundingClientRect();

  const offsetTop =
    ((y - referenceClientBounds.top) * 100) / referenceClientBounds.height;
  const offsetLeft =
    ((x - referenceClientBounds.left) * 100) / referenceClientBounds.width;

  return {
    offsetTop,
    offsetLeft,
  };
}

/**
 * Generate XPath for element
 */
export const getXPathForElement = (element: HTMLElement, useId: boolean = false): string => {
  if (element.id && useId) {
    return `//*[@id="${element.id}"]`;
  }

  let nodeElem: HTMLElement | null = element;
  const parts: string[] = [];
  
  while (nodeElem && Node.ELEMENT_NODE === nodeElem.nodeType) {
    let nbOfPreviousSiblings = 0;
    let hasNextSiblings = false;
    let sibling = nodeElem.previousSibling;
    
    while (sibling) {
      if (
        sibling.nodeType !== Node.DOCUMENT_TYPE_NODE &&
        sibling.nodeName === nodeElem.nodeName
      ) {
        nbOfPreviousSiblings++;
      }
      sibling = sibling.previousSibling;
    }
    
    sibling = nodeElem.nextSibling;
    while (sibling) {
      if (sibling.nodeName === nodeElem.nodeName) {
        hasNextSiblings = true;
        break;
      }
      sibling = sibling.nextSibling;
    }
    
    const prefix = nodeElem.prefix ? `${nodeElem.prefix}:` : '';
    const nth =
      nbOfPreviousSiblings || hasNextSiblings
        ? `[${nbOfPreviousSiblings + 1}]`
        : '';
    parts.push(prefix + nodeElem.localName + nth);
    nodeElem = nodeElem.parentElement;
  }
  
  return parts.length ? `/${parts.reverse().join('/')}` : '';
};

/**
 * Get element by XPath
 */
export function getElementByXPath(xpath: string): HTMLElement | null {
  try {
    const result = document.evaluate(
      xpath,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    );
    return result.singleNodeValue as HTMLElement | null;
  } catch (error) {
    console.warn('Invalid XPath:', xpath, error);
    return null;
  }
}

/**
 * Get element display name for UI
 */
export function getElementDisplayName(element: HTMLElement): string {
  const tagName = element.tagName.toLowerCase();
  const className = element.className;
  const id = element.id;
  
  if (id) {
    return `${tagName}#${id}`;
  } else if (className && typeof className === 'string') {
    const firstClass = className.split(' ')[0];
    return `${tagName}.${firstClass}`;
  }
  return tagName;
}

/**
 * Extract comprehensive element information
 */
export interface ElementInfo {
  id: string;
  tagName: string;
  className: string;
  xpath: string;
  boundingRect: DOMRect;
  textContent: string;
  attributes: Record<string, string>;
  element: HTMLElement;
  timestamp: number;
}

export function extractElementInfo(element: HTMLElement): ElementInfo {
  const rect = element.getBoundingClientRect();
  const attributes: Record<string, string> = {};
  
  // Extract all attributes
  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i];
    attributes[attr.name] = attr.value;
  }
  
  return {
    id: element.id || `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    tagName: element.tagName.toLowerCase(),
    className: element.className || '',
    xpath: getXPathForElement(element, true),
    boundingRect: rect,
    textContent: element.textContent?.trim() || '',
    attributes,
    element,
    timestamp: Date.now(),
  };
}

/**
 * Check if element is visible
 */
export function isElementVisible(element: HTMLElement): boolean {
  const rect = element.getBoundingClientRect();
  const style = window.getComputedStyle(element);
  
  return (
    rect.width > 0 &&
    rect.height > 0 &&
    style.visibility !== 'hidden' &&
    style.display !== 'none' &&
    parseFloat(style.opacity) > 0
  );
}

/**
 * Scroll element into view if needed
 */
export function scrollElementIntoView(element: HTMLElement): void {
  const rect = element.getBoundingClientRect();
  const isInViewport = (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
  
  if (!isInViewport) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center'
    });
  }
}

/**
 * Generate unique selector for element
 */
export function generateUniqueSelector(element: HTMLElement): string {
  // Try ID first
  if (element.id) {
    return `#${element.id}`;
  }
  
  // Try unique class combination
  if (element.className) {
    const classes = element.className.split(' ').filter(Boolean);
    if (classes.length > 0) {
      const selector = `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      if (document.querySelectorAll(selector).length === 1) {
        return selector;
      }
    }
  }
  
  // Fall back to XPath
  return getXPathForElement(element, false);
}
