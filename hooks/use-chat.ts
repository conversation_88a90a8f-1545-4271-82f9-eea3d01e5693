import { useState, useCallback, useRef, useEffect } from 'react'
import type { ChatMessage, AgentStatus } from '../components/chat/chat-panel'
import type { SelectedElement } from './use-element-selector'

export interface UseChatOptions {
  onMessageSend?: (message: string, context: SelectedElement[]) => Promise<void>
  onSelectionModeChange?: (isActive: boolean) => void
}

// Storage key for persisting chat panel state
const CHAT_PANEL_STORAGE_KEY = 'snapmark-chat-panel-state'

interface PersistedChatState {
  isChatOpen: boolean
  isSelectionMode: boolean
}

const loadPersistedChatState = (): Partial<PersistedChatState> => {
  try {
    const stored = sessionStorage.getItem(CHAT_PANEL_STORAGE_KEY)
    return stored ? JSON.parse(stored) : {}
  } catch (error) {
    console.warn('[useChat] Failed to load persisted state:', error)
    return {}
  }
}

const savePersistedChatState = (state: PersistedChatState) => {
  try {
    sessionStorage.setItem(CHAT_PANEL_STORAGE_KEY, JSON.stringify(state))
  } catch (error) {
    console.warn('[useChat] Failed to save persisted state:', error)
  }
}

export function useChat({
  onMessageSend,
  onSelectionModeChange
}: UseChatOptions = {}) {
  // Load persisted state on initialization
  const persistedState = loadPersistedChatState()

  const [isChatOpen, setIsChatOpen] = useState(persistedState.isChatOpen ?? false)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [agentStatus, setAgentStatus] = useState<AgentStatus>('idle')
  const [isSelectionMode, setIsSelectionMode] = useState(persistedState.isSelectionMode ?? false)

  // Message ID counter
  const messageIdRef = useRef(0)

  // Handle opening chat panel
  const openChat = useCallback(() => {
    setIsChatOpen(true)
  }, [])

  // Handle closing chat panel
  const closeChat = useCallback(() => {
    setIsChatOpen(false)
    // Also exit selection mode when closing chat
    if (isSelectionMode) {
      setIsSelectionMode(false)
      onSelectionModeChange?.(false)
    }
  }, [isSelectionMode, onSelectionModeChange])

  // Toggle chat panel (core chat button functionality)
  const toggleChat = useCallback(() => {
    if (isChatOpen) {
      // If chat is open, close it
      closeChat()
    } else {
      // If chat is closed, open it and enter selection mode
      openChat()
      setIsSelectionMode(true)
      onSelectionModeChange?.(true)
    }
  }, [isChatOpen, closeChat, openChat, onSelectionModeChange])

  // Toggle selection mode (when chat is already open)
  const toggleSelectionMode = useCallback(() => {
    const newSelectionMode = !isSelectionMode
    setIsSelectionMode(newSelectionMode)
    onSelectionModeChange?.(newSelectionMode)
  }, [isSelectionMode, onSelectionModeChange])

  // Exit selection mode only (keep chat open)
  const exitSelectionMode = useCallback(() => {
    if (isSelectionMode) {
      setIsSelectionMode(false)
      onSelectionModeChange?.(false)
    }
  }, [isSelectionMode, onSelectionModeChange])

  // Add user message
  const addUserMessage = useCallback((content: string, context?: SelectedElement[]) => {
    const message: ChatMessage = {
      id: `user-${++messageIdRef.current}`,
      content,
      sender: 'user',
      timestamp: Date.now(),
      context
    }
    setMessages(prev => [...prev, message])
    return message
  }, [])

  // Add agent message
  const addAgentMessage = useCallback((content: string) => {
    const message: ChatMessage = {
      id: `agent-${++messageIdRef.current}`,
      content,
      sender: 'agent',
      timestamp: Date.now()
    }
    setMessages(prev => [...prev, message])
    return message
  }, [])

  // Handle sending message
  const sendMessage = useCallback(async (content: string, context: SelectedElement[]) => {
    if (!content.trim()) return

    // Add user message
    addUserMessage(content, context)
    
    // Set agent status to thinking
    setAgentStatus('thinking')
    
    try {
      // Call external handler if provided
      if (onMessageSend) {
        setAgentStatus('working')
        await onMessageSend(content, context)
        setAgentStatus('completed')
        
        // Simulate agent response for now
        setTimeout(() => {
          addAgentMessage(`I'll help you with "${content}" on the ${context.length} selected element(s).`)
          setAgentStatus('idle')
        }, 1000)
      } else {
        // Default behavior - just echo back
        setTimeout(() => {
          addAgentMessage(`I received your message: "${content}" with ${context.length} selected element(s). (No agent handler configured)`)
          setAgentStatus('idle')
        }, 1500)
      }
      
      // Auto-exit selection mode after sending
      exitSelectionMode()
      
    } catch (error) {
      console.error('Failed to send message:', error)
      setAgentStatus('failed')
      addAgentMessage('Sorry, I encountered an error processing your request.')
      
      // Reset status after error
      setTimeout(() => setAgentStatus('idle'), 3000)
    }
  }, [addUserMessage, addAgentMessage, onMessageSend, exitSelectionMode])

  // Clear all messages
  const clearMessages = useCallback(() => {
    setMessages([])
    setAgentStatus('idle')
  }, [])

  // Load chat history from storage
  useEffect(() => {
    const stored = sessionStorage.getItem('snapmark-chat-history')
    if (stored) {
      try {
        const parsedMessages = JSON.parse(stored)
        setMessages(parsedMessages)
      } catch (e) {
        console.warn('Failed to load chat history')
      }
    }
  }, [])

  // Save chat history to storage
  useEffect(() => {
    if (messages.length > 0) {
      sessionStorage.setItem('snapmark-chat-history', JSON.stringify(messages))
    }
  }, [messages])

  // Global keyboard shortcuts for chat
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K to open chat
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        if (!isChatOpen) {
          toggleChat()
        }
      }
      
      // Escape to exit selection mode (but keep chat open)
      if (e.key === 'Escape' && isChatOpen && isSelectionMode) {
        exitSelectionMode()
      }
    }

    document.addEventListener('keydown', handleGlobalKeyDown)
    return () => document.removeEventListener('keydown', handleGlobalKeyDown)
  }, [isChatOpen, isSelectionMode, toggleChat, exitSelectionMode])

  return {
    // State
    isChatOpen,
    messages,
    agentStatus,
    isSelectionMode,
    
    // Actions
    openChat,
    closeChat,
    toggleChat,
    toggleSelectionMode,
    exitSelectionMode,
    sendMessage,
    clearMessages,
    addUserMessage,
    addAgentMessage,
    
    // Status setters (for external control)
    setAgentStatus
  }
}
