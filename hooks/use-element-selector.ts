import { useState, useCallback, useEffect, useRef } from 'react'

export interface SelectedElement {
  id: string
  tagName: string
  className: string
  xpath: string
  boundingRect: DOMRect
  textContent: string
  element: HTMLElement
}

export interface ElementSelectorOptions {
  excludeSelectors?: string[]
  onElementHover?: (element: HTMLElement | null) => void
  onElementSelect?: (element: SelectedElement) => void
}

// Stagewise's exact getElementAtPoint implementation
const getElementAtPoint = (x: number, y: number): HTMLElement => {
  const elementsBelowAnnotation = document.elementsFromPoint(x, y);

  const refElement =
    (elementsBelowAnnotation.find(
      (element) =>
        !element.closest('svg') &&
        !element.closest('.snapmark-toolbar') &&
        !element.closest('[data-snapmark]'),
    ) as HTMLElement) || document.body;

  return refElement;
};

// Generate XPath for element (stagewise method)
const getXPath = (element: HTMLElement): string => {
  if (element.id) {
    return `//*[@id="${element.id}"]`
  }
  
  const parts: string[] = []
  let currentElement: HTMLElement | null = element
  
  while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE) {
    let tagName = currentElement.tagName.toLowerCase()
    let index = 1
    
    if (currentElement.previousElementSibling) {
      let sibling = currentElement.previousElementSibling
      while (sibling) {
        if (sibling.tagName.toLowerCase() === tagName) {
          index++
        }
        sibling = sibling.previousElementSibling
      }
    }
    
    if (index > 1) {
      tagName += `[${index}]`
    }
    
    parts.unshift(tagName)
    currentElement = currentElement.parentElement
  }
  
  return '/' + parts.join('/')
}

export function useElementSelector({
  onElementHover,
  onElementSelect
}: ElementSelectorOptions = {}) {
  const [isSelecting, setIsSelecting] = useState(false)
  const [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null)
  const [selectedElements, setSelectedElements] = useState<SelectedElement[]>([])
  const overlayRef = useRef<HTMLDivElement | null>(null)
  
  // Stagewise's mouse tracking implementation
  const mouseState = useRef<{
    lastX?: number;
    lastY?: number;
    velocity: number;
    lastTimestamp: number;
  } | null>(null);
  
  const nextUpdateTimeout = useRef<NodeJS.Timeout | null>(null);
  const lastHoveredElement = useRef<HTMLElement | null>(null);

  // Create overlay element
  useEffect(() => {
    if (!overlayRef.current) {
      overlayRef.current = document.createElement('div')
      overlayRef.current.setAttribute('data-snapmark', 'snapmark-overlay')
      overlayRef.current.style.cssText = `
        position: fixed;
        pointer-events: none;
        z-index: 9997;
        border: 2px dotted rgba(59, 130, 246, 0.7);
        background-color: rgba(59, 130, 246, 0.05);
        border-radius: 4px;
        transition: all 0.1s ease-in-out;
        display: none;
      `
      document.body.appendChild(overlayRef.current)
    }
    
    return () => {
      if (overlayRef.current) {
        try {
          document.body.removeChild(overlayRef.current)
        } catch (e) {
          // Already removed
        }
        overlayRef.current = null
      }
    }
  }, [])

  // Update overlay position (stagewise method)
  const updateOverlay = useCallback((element: HTMLElement | null) => {
    if (!overlayRef.current) return
    
    if (element) {
      const rect = element.getBoundingClientRect()
      overlayRef.current.style.display = 'block'
      overlayRef.current.style.left = `${rect.left}px`
      overlayRef.current.style.top = `${rect.top}px`
      overlayRef.current.style.width = `${rect.width}px`
      overlayRef.current.style.height = `${rect.height}px`
    } else {
      overlayRef.current.style.display = 'none'
    }
  }, [])

  // Stagewise's updateHoveredElement logic
  const updateHoveredElement = useCallback(() => {
    if (!mouseState.current) return;
    
    const refElement = getElementAtPoint(
      mouseState.current.lastX || 0,
      mouseState.current.lastY || 0,
    );
    
    // Check if element is already selected
    const isAlreadySelected = selectedElements.some(sel => sel.element === refElement);
    if (isAlreadySelected) {
      lastHoveredElement.current = null;
      setHoveredElement(null);
      updateOverlay(null);
      onElementHover?.(null);
      return;
    }
    
    if (lastHoveredElement.current !== refElement) {
      lastHoveredElement.current = refElement;
      setHoveredElement(refElement);
      updateOverlay(refElement);
      onElementHover?.(refElement);
    }
  }, [selectedElements, updateOverlay, onElementHover]);

  // Stagewise's mouse move handler with velocity tracking
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isSelecting) return;
    
    // Calculate mouse velocity
    const currentTimestamp = performance.now();
    const deltaX = event.clientX - (mouseState.current?.lastX ?? event.clientX);
    const deltaY = event.clientY - (mouseState.current?.lastY ?? event.clientY);
    const deltaTime = currentTimestamp - (mouseState.current?.lastTimestamp ?? currentTimestamp);
    
    const distance = Math.hypot(deltaX, deltaY);
    
    mouseState.current = {
      lastX: deltaTime > 0 ? event.clientX : mouseState.current?.lastX,
      lastY: deltaTime > 0 ? event.clientY : mouseState.current?.lastY,
      velocity: deltaTime > 0 ? (distance / deltaTime) * 1000 : 0,
      lastTimestamp: currentTimestamp,
    };

    // If velocity exceeds 30 pixels per second, delay update (stagewise logic)
    if (mouseState.current?.velocity > 30) {
      if (nextUpdateTimeout.current) {
        clearTimeout(nextUpdateTimeout.current);
      }
      nextUpdateTimeout.current = setTimeout(updateHoveredElement, 1000 / 28);
    } else if (!nextUpdateTimeout.current) {
      nextUpdateTimeout.current = setTimeout(updateHoveredElement, 1000 / 28);
    }
  }, [isSelecting, updateHoveredElement]);

  // Handle click to select element
  const handleClick = useCallback((event: MouseEvent) => {
    if (!isSelecting) return;
    
    event.preventDefault();
    event.stopPropagation();

    if (!lastHoveredElement.current) return;
    
    // Check if already selected
    const isAlreadySelected = selectedElements.some(sel => sel.element === lastHoveredElement.current);
    if (isAlreadySelected) return;

    const element = lastHoveredElement.current;
    const selectedElement: SelectedElement = {
      id: element.id || `element-${Date.now()}`,
      tagName: element.tagName.toLowerCase(),
      className: element.className,
      xpath: getXPath(element),
      boundingRect: element.getBoundingClientRect(),
      textContent: element.textContent?.trim() || '',
      element: element
    };

    setSelectedElements(prev => [...prev, selectedElement]);
    onElementSelect?.(selectedElement);

    // Add visual feedback to selected element
    element.style.outline = '2px solid #10b981';
    element.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
  }, [isSelecting, selectedElements, onElementSelect]);

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isSelecting) return;
    
    switch (event.key) {
      case 'Escape':
        setIsSelecting(false);
        break;
      case 'Delete':
      case 'Backspace':
        // Remove last selected element
        setSelectedElements(prev => {
          if (prev.length > 0) {
            const lastElement = prev[prev.length - 1];
            if (lastElement.element) {
              lastElement.element.style.outline = '';
              lastElement.element.style.backgroundColor = '';
            }
            return prev.slice(0, -1);
          }
          return prev;
        });
        break;
    }
  }, [isSelecting]);

  // Store event handlers in refs to avoid dependency issues
  const handleMouseMoveRef = useRef<(e: MouseEvent) => void>();
  const handleClickRef = useRef<(e: MouseEvent) => void>();
  const handleKeyDownRef = useRef<(e: KeyboardEvent) => void>();

  // Update refs with current handlers
  handleMouseMoveRef.current = handleMouseMove;
  handleClickRef.current = handleClick;
  handleKeyDownRef.current = handleKeyDown;

  // Stop selection mode
  const stopSelection = useCallback(() => {
    setIsSelecting(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    
    // Clear timeouts
    if (nextUpdateTimeout.current) {
      clearTimeout(nextUpdateTimeout.current);
      nextUpdateTimeout.current = null;
    }
    
    // Reset state
    lastHoveredElement.current = null;
    setHoveredElement(null);
    updateOverlay(null);
    
    // Remove event listeners using wrapper functions
    const mouseMoveWrapper = (e: MouseEvent) => handleMouseMoveRef.current?.(e);
    const clickWrapper = (e: MouseEvent) => handleClickRef.current?.(e);
    const keyDownWrapper = (e: KeyboardEvent) => handleKeyDownRef.current?.(e);
    
    document.removeEventListener('mousemove', mouseMoveWrapper, true);
    document.removeEventListener('click', clickWrapper, true);
    document.removeEventListener('keydown', keyDownWrapper, true);
  }, [updateOverlay]);

  // Start selection mode
  const startSelection = useCallback(() => {
    setIsSelecting(true);
    document.body.style.cursor = 'crosshair';
    document.body.style.userSelect = 'none';
    
    // Add event listeners using wrapper functions
    const mouseMoveWrapper = (e: MouseEvent) => handleMouseMoveRef.current?.(e);
    const clickWrapper = (e: MouseEvent) => handleClickRef.current?.(e);
    const keyDownWrapper = (e: KeyboardEvent) => handleKeyDownRef.current?.(e);
    
    document.addEventListener('mousemove', mouseMoveWrapper, true);
    document.addEventListener('click', clickWrapper, true);
    document.addEventListener('keydown', keyDownWrapper, true);
  }, []);

  // Toggle selection mode
  const toggleSelection = useCallback(() => {
    if (isSelecting) {
      stopSelection();
    } else {
      startSelection();
    }
  }, [isSelecting, startSelection, stopSelection]);

  // Clear selected elements
  const clearSelectedElements = useCallback(() => {
    setSelectedElements(prev => {
      // Remove visual feedback from selected elements
      prev.forEach(({ element }) => {
        if (element) {
          element.style.outline = '';
          element.style.backgroundColor = '';
        }
      });
      return [];
    });
  }, []);

  // Remove specific element
  const removeSelectedElement = useCallback((id: string) => {
    setSelectedElements(prev => {
      const element = prev.find(el => el.id === id);
      if (element?.element) {
        element.element.style.outline = '';
        element.element.style.backgroundColor = '';
      }
      return prev.filter(el => el.id !== id);
    });
  }, []);

  return {
    isSelecting,
    hoveredElement,
    selectedElements,
    startSelection,
    stopSelection,
    toggleSelection,
    clearSelectedElements,
    removeSelectedElement
  };
}